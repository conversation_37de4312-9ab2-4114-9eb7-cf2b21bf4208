#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

// fRead: 从输入文件读取多项式系数、次数和模数
void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

// fCheck: 检查计算结果与期望输出的一致性
void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

// fWrite: 将多项式乘积写入输出文件
void fWrite(int *ab, int n, int input_id) {
    std::string str1 = "files/";
    std::string str2 = std::to_string(input_id);
    std::string strout = str1 + str2 + ".out";
    std::ofstream fout(strout);
    for (int i = 0; i < n * 2 - 1; i++) {
        fout << ab[i] << '\n';
    }
}

// qpow: 计算快速幂 (a^b) % p
inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// CUDA Kernel: 位反转置换
__global__ void bit_reverse_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

// CUDA Kernel: Radix-2 NTT蝶形运算（用于混合radix）
__global__ void ntt_radix2_kernel(int *data, int len, int wn, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;

    if(block_id * len >= n) return;

    int base = block_id * len;
    if(base + local_id + half_len >= n) return;

    // 计算旋转因子 w^local_id
    long long w = 1;
    for(int i = 0; i < local_id; i++) {
        w = (w * wn) % p;
    }

    int u = data[base + local_id];
    long long v = (1LL * data[base + local_id + half_len] * w) % p;

    data[base + local_id] = (u + v) % p;
    data[base + local_id + half_len] = (u - v + p) % p;
}

// CUDA Kernel: 正确的Radix-4 NTT蝶形运算（预计算版本）
__global__ void ntt_radix4_kernel(int *data, int len, const int *twiddles, int J, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int m = len >> 2;
    int block_id = idx / m;
    int j = idx % m;

    if(block_id * len >= n) return;

    int base = block_id * len;
    if(base + j + 3 * m >= n) return;

    // 预先计算的旋转因子
    int w1 = twiddles[j];
    int w2 = twiddles[j + m];
    int w3 = twiddles[j + 2 * m];

    // 读取四个数据点
    int A = data[base + j];
    int B = data[base + j + m];
    int C = data[base + j + 2 * m];
    int D = data[base + j + 3 * m];

    // 应用旋转因子
    B = (1LL * B * w1) % p;
    C = (1LL * C * w2) % p;
    D = (1LL * D * w3) % p;

    // 标准Radix-4蝶形运算
    int T0 = (A + C) % p;
    int T1 = (A + p - C) % p;
    int T2 = (B + D) % p;
    int T3 = (1LL * (B + p - D) * J) % p;

    // 输出四个结果
    data[base + j] = (T0 + T2) % p;
    data[base + j + m] = (T1 + T3) % p;
    data[base + j + 2 * m] = (T0 + p - T2) % p;
    data[base + j + 3 * m] = (T1 + p - T3) % p;
}

// CUDA Kernel: 数组缩放
__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

__global__ void pointwise_mult_kernel(int *a, int *b, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        a[idx] = (1LL * a[idx] * b[idx]) % p;
    }
}

void cuda_ntt_radix4(int *h_data, int n, bool inverse, int p);

void cuda_poly_multiply_radix4(int* a, int* b, int* ab, int n, int p) {
    int N = 1;
    while(N < (2 * n - 1)) N <<= 2;

    std::vector<int> h_a(N, 0), h_b(N, 0);
    for(int i = 0; i < n; i++) {
        h_a[i] = a[i];
        h_b[i] = b[i];
    }

    cuda_ntt_radix4(h_a.data(), N, false, p);
    cuda_ntt_radix4(h_b.data(), N, false, p);

    int *d_a, *d_b;
    CHECK_CUDA(cudaMalloc(&d_a, N * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_b, N * sizeof(int)));
    CHECK_CUDA(cudaMemcpy(d_a, h_a.data(), N * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_b, h_b.data(), N * sizeof(int), cudaMemcpyHostToDevice));

    int threads = std::min(1024, N);
    int blocks = (N + threads - 1) / threads;
    pointwise_mult_kernel<<<blocks, threads>>>(d_a, d_b, N, p);
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaMemcpy(h_a.data(), d_a, N * sizeof(int), cudaMemcpyDeviceToHost));
    
    cuda_ntt_radix4(h_a.data(), N, true, p);

    for(int i = 0; i < 2 * n - 1; i++) {
        ab[i] = h_a[i];
    }

    CHECK_CUDA(cudaFree(d_a));
    CHECK_CUDA(cudaFree(d_b));
}

// Host function: CUDA 混合 Radix-2/4 NTT
void cuda_ntt_radix4(int *h_data, int n, bool inverse, int p) {
    // 分配设备内存
    int *d_data, *d_rev, *d_twiddles;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles, (3 * n / 4) * sizeof(int)));

    // 检查是否需要初始radix-2阶段
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    bool need_initial_radix2 = (lg % 2 != 0);

    // 准备位反转表
    std::vector<int> rev(n);

    if(need_initial_radix2) {
        // 标准二进制位反转
        for(int i = 0; i < n; i++){
            rev[i] = 0;
            for(int j = 0; j < lg; j++)
                if(i & (1 << j)) rev[i] |= 1 << (lg - 1 - j);
        }
    } else {
        // 4进制位反转
        int pairs = lg / 2;
        for(int i = 0; i < n; i++){
            rev[i] = 0;
            int tmp = i;
            for(int j = 0; j < pairs; j++){
                rev[i] = (rev[i] << 2) | (tmp & 3);
                tmp >>= 2;
            }
        }
    }

    // 拷贝数据到设备
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));

    // 位反转置换
    int threads = std::min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());



    // 如果需要，先进行一层radix-2变换
    if(need_initial_radix2) {
        int len = 2;
        int wn = qpow(3, (p-1)/len, p);
        if(inverse) wn = qpow(wn, p-2, p);

        int half_len = len >> 1;
        int total_butterflies = n / len * half_len;
        threads = std::min(1024, total_butterflies);
        blocks = (total_butterflies + threads - 1) / threads;

        ntt_radix2_kernel<<<blocks, threads>>>(d_data, len, wn, p, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    std::vector<int> h_twiddles(3 * n / 4);

    if(need_initial_radix2) {
        // 奇数log：完全使用radix-2
        for(int len = 2; len <= n; len <<= 1) {
            int wn = qpow(3, (p-1)/len, p);
            if(inverse) wn = qpow(wn, p-2, p);

            int half_len = len >> 1;
            int total_butterflies = n / len * half_len;
            threads = std::min(1024, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;

            ntt_radix2_kernel<<<blocks, threads>>>(d_data, len, wn, p, n);
            CHECK_CUDA(cudaDeviceSynchronize());
        }
    } else {
        // 偶数log：使用radix-4
        for(int len = 4; len <= n; len <<= 2) {
            int m = len >> 2;
            long long wn = qpow(3, (p - 1) / len, p);
            if (inverse) wn = qpow(wn, p - 2, p);

            // 计算虚数单位 J = wn^m
            int J = qpow(wn, m, p);

            // 预计算旋转因子
            long long current_w = 1;
            for (int k = 0; k < m; ++k) {
                h_twiddles[k] = static_cast<int>(current_w);
                long long w2 = (current_w * current_w) % p;
                h_twiddles[k + m] = static_cast<int>(w2);
                long long w3 = (w2 * current_w) % p;
                h_twiddles[k + 2 * m] = static_cast<int>(w3);
                current_w = (current_w * wn) % p;
            }
            CHECK_CUDA(cudaMemcpy(d_twiddles, h_twiddles.data(), (3 * m) * sizeof(int), cudaMemcpyHostToDevice));

            int total_butterflies = n / len * m;
            threads = std::min(1024, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;

            ntt_radix4_kernel<<<blocks, threads>>>(d_data, len, d_twiddles, J, p, n);
            CHECK_CUDA(cudaDeviceSynchronize());
        }
    }


    // 逆变换的最终缩放
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        threads = std::min(1024, n);
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    // 拷贝结果回主机
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));

    // 清理
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
    CHECK_CUDA(cudaFree(d_twiddles));
}

// 主函数
int main() {
    // 检查CUDA设备
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }

    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);

    // 测试数组
    int a[300000], b[300000], ab[300000];

    printf("\nCUDA Radix-4 NTT 实现测试:\n");
    printf("================================================================\n");

    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));

        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);

        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_radix4(a, b, ab, n, p);
        auto end = std::chrono::high_resolution_clock::now();

        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);

        printf("----------------------------------------\n");
    }

    return 0;
}
